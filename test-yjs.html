<!DOCTYPE html>
<html>
<head>
    <title>YJS Test</title>
    <script src="https://unpkg.com/yjs@13.6.10/dist/yjs.js"></script>
</head>
<body>
    <h1>YJS Test</h1>
    <textarea id="editor" rows="10" cols="50"></textarea>
    <div id="log"></div>

    <script>
        const editor = document.getElementById('editor');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            console.log(message);
            logDiv.innerHTML += '<div>' + message + '</div>';
        }
        
        // Create YJS document
        const doc = new Y.Doc();
        const text = doc.getText('content');
        
        // Test basic YJS functionality
        log('YJS Doc created');
        
        // Listen for text changes
        text.observe((event) => {
            log('YJS text observed: ' + text.toString());
            editor.value = text.toString();
        });
        
        // Listen for document updates
        doc.on('update', (update, origin) => {
            log('YJS doc update - origin: ' + origin + ', size: ' + update.length);
        });
        
        // Handle input
        editor.addEventListener('input', (e) => {
            const content = e.target.value;
            log('Input detected: ' + content);
            
            // Update YJS text
            text.delete(0, text.length);
            text.insert(0, content);
        });
        
        log('Test setup complete');
    </script>
</body>
</html>
