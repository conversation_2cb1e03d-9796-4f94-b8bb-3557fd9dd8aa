<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working YJS Realtime Collaboration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
        }
        .connected { 
            background-color: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb;
        }
        .disconnected { 
            background-color: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .controls button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn-primary { 
            background-color: #007bff; 
            color: white; 
        }
        .btn-primary:hover { 
            background-color: #0056b3; 
        }
        .btn-secondary { 
            background-color: #6c757d; 
            color: white; 
        }
        .btn-primary:disabled { 
            background-color: #ccc; 
            cursor: not-allowed;
        }
        .editor {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s;
        }
        .editor:focus {
            outline: none;
            border-color: #007bff;
        }
        .editor:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }
        .users {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .user {
            display: inline-block;
            margin: 3px;
            padding: 6px 12px;
            background-color: #007bff;
            color: white;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .stats {
            margin: 15px 0;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
        }
        .log {
            height: 250px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 4px;
            word-wrap: break-word;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: 500;
        }
        .log-error { color: #dc3545; }
        .log-warning { color: #fd7e14; }
        .log-success { color: #28a745; }
        h1 { color: #343a40; margin-bottom: 30px; }
        h3 { color: #495057; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>🚀 YJS Realtime Collaboration Demo</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">Disconnected - Click Connect to start</div>
        
        <div class="controls">
            <input type="text" id="documentId" value="demo-document" placeholder="Document ID" />
            <input type="text" id="userId" value="" placeholder="User ID" />
            <button id="connectBtn" class="btn-primary">Connect</button>
            <button id="disconnectBtn" class="btn-secondary" disabled>Disconnect</button>
        </div>
        
        <div class="users">
            <strong>👥 Connected Users:</strong>
            <div id="userList">None</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📝 Collaborative Text Editor</h3>
        <p><em>Start typing below. Open this page in multiple tabs or browsers to see real-time collaboration!</em></p>
        <textarea id="editor" class="editor" placeholder="Start typing to collaborate in real-time..." disabled></textarea>
    </div>
    
    <div class="container">
        <div class="stats">
            <strong>📊 Server Statistics:</strong>
            <div id="stats">Click Connect to load stats...</div>
        </div>
    </div>
    
    <div class="container">
        <h3>📋 Event Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Load libraries from server -->
    <script src="/lib/socket.io/client-dist/socket.io.min.js"></script>
    <script src="/lib/yjs/dist/yjs.js"></script>
    
    <script>
        class YjsCollaborationClient {
            constructor() {
                this.socket = null;
                this.doc = null;
                this.text = null;
                this.connected = false;
                this.documentId = null;
                this.userId = null;
                this.users = new Set();
                this.updatingFromRemote = false;
                
                this.initializeElements();
                this.setupEventListeners();
                this.generateUserId();
                this.checkLibraries();
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.documentIdEl = document.getElementById('documentId');
                this.userIdEl = document.getElementById('userId');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.editorEl = document.getElementById('editor');
                this.userListEl = document.getElementById('userList');
                this.statsEl = document.getElementById('stats');
                this.logEl = document.getElementById('log');
            }
            
            checkLibraries() {
                if (typeof io === 'undefined') {
                    this.log('❌ Socket.IO library not loaded!', 'error');
                    return false;
                }
                if (typeof Y === 'undefined') {
                    this.log('❌ YJS library not loaded!', 'error');
                    return false;
                }
                this.log('✅ All libraries loaded successfully', 'success');
                return true;
            }
            
            generateUserId() {
                this.userIdEl.value = 'user-' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                
                this.editorEl.addEventListener('input', (e) => {
                    if (!this.updatingFromRemote && this.text) {
                        const content = e.target.value;
                        this.text.delete(0, this.text.length);
                        this.text.insert(0, content);
                    }
                });
            }
            
            connect() {
                if (!this.checkLibraries()) {
                    alert('Required libraries not loaded. Please refresh the page.');
                    return;
                }
                
                const documentId = this.documentIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.documentId = documentId;
                this.userId = userId;
                
                // Initialize YJS document
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');
                
                // Setup YJS event listeners
                this.text.observe((event) => {
                    if (!this.updatingFromRemote) {
                        this.editorEl.value = this.text.toString();
                    }
                });
                
                this.doc.on('update', (update, origin) => {
                    if (origin !== this.socket?.id && this.connected) {
                        this.socket.emit('yjs-update', {
                            documentId: this.documentId,
                            update: Array.from(update)
                        });
                    }
                });
                
                this.log('🔌 Connecting to server...');
                
                this.socket = io('http://localhost:3000', {
                    transports: ['websocket', 'polling'],
                    timeout: 10000
                });
                
                this.setupSocketListeners();
            }
            
            setupSocketListeners() {
                this.socket.on('connect', () => {
                    this.log('✅ Connected to server', 'success');
                    this.updateConnectionStatus(true);
                    
                    this.socket.emit('join-document', {
                        documentId: this.documentId,
                        userId: this.userId
                    });
                });
                
                this.socket.on('disconnect', (reason) => {
                    this.log(`❌ Disconnected: ${reason}`, 'error');
                    this.updateConnectionStatus(false);
                });
                
                this.socket.on('connect_error', (error) => {
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });
                
                this.socket.on('joined-document', (data) => {
                    this.log(`🏠 Joined document: ${data.documentId} (${data.connectedUsers} users)`, 'success');
                });
                
                this.socket.on('yjs-update', (data) => {
                    if (data.origin !== this.socket.id) {
                        this.updatingFromRemote = true;
                        const update = new Uint8Array(data.update);
                        Y.applyUpdate(this.doc, update, this.socket.id);
                        this.editorEl.value = this.text.toString();
                        this.updatingFromRemote = false;
                        this.log('📥 Received update from remote');
                    }
                });
                
                this.socket.on('sync-response', (data) => {
                    this.updatingFromRemote = true;
                    const state = new Uint8Array(data.state);
                    Y.applyUpdate(this.doc, state, this.socket.id);
                    this.editorEl.value = this.text.toString();
                    this.updatingFromRemote = false;
                    this.log(`🔄 Document synced (${data.type})`, 'success');
                });
                
                this.socket.on('user-joined', (data) => {
                    this.users.add(data.userId);
                    this.updateUserList();
                    this.log(`👋 User joined: ${data.userId}`, 'success');
                });
                
                this.socket.on('user-left', (data) => {
                    this.users.delete(data.userId);
                    this.updateUserList();
                    this.log(`👋 User left: ${data.userId}`);
                });
                
                this.socket.on('error', (error) => {
                    this.log(`❌ Server error: ${error.message}`, 'error');
                });
            }
            
            disconnect() {
                if (this.socket) {
                    this.socket.disconnect();
                }
                this.updateConnectionStatus(false);
                this.log('🔌 Disconnected by user');
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 
                    `Connected to ${this.documentId} as ${this.userId}` : 
                    'Disconnected - Click Connect to start';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
                
                if (!connected) {
                    this.users.clear();
                    this.updateUserList();
                    clearInterval(this.statsInterval);
                } else {
                    this.startStatsUpdates();
                }
            }
            
            updateUserList() {
                if (this.users.size === 0) {
                    this.userListEl.textContent = 'None';
                } else {
                    this.userListEl.innerHTML = '';
                    this.users.forEach(userId => {
                        const userEl = document.createElement('span');
                        userEl.className = 'user';
                        userEl.textContent = userId;
                        this.userListEl.appendChild(userEl);
                    });
                }
            }
            
            startStatsUpdates() {
                this.fetchStats();
                this.statsInterval = setInterval(() => {
                    this.fetchStats();
                }, 5000);
            }
            
            fetchStats() {
                fetch('http://localhost:3000/api/stats')
                    .then(response => response.json())
                    .then(stats => {
                        this.statsEl.innerHTML = `
                            <div>📊 Total Connections: ${stats.connections.totalConnections}</div>
                            <div>📄 Active Documents: ${stats.documents.totalDocuments}</div>
                            <div>🕒 Last Updated: ${new Date(stats.timestamp).toLocaleTimeString()}</div>
                        `;
                    })
                    .catch(error => {
                        this.log(`Failed to fetch stats: ${error.message}`, 'warning');
                    });
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const timestampSpan = document.createElement('span');
                timestampSpan.className = 'log-timestamp';
                timestampSpan.textContent = `[${timestamp}] `;
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                messageSpan.className = `log-${type}`;
                
                logEntry.appendChild(timestampSpan);
                logEntry.appendChild(messageSpan);
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize the client
        const client = new YjsCollaborationClient();
        
        // Test server on page load
        window.addEventListener('load', () => {
            client.log('🚀 YJS Collaboration Client loaded');
            
            fetch('http://localhost:3000/health')
                .then(response => response.json())
                .then(data => {
                    client.log(`✅ Server health: ${data.status}`, 'success');
                })
                .catch(error => {
                    client.log(`❌ Server not reachable: ${error.message}`, 'error');
                    client.log('Make sure the server is running: npm start', 'warning');
                });
        });
    </script>
</body>
</html>
