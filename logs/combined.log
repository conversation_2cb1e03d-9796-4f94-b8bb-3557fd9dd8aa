{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.439Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.444Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.446Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.200Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.204Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.205Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.205Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.206Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.206Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.214Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"production","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.215Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.354Z"}
{"connectionId":"BCol_1xASC5fplh6AAAB","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.357Z","userId":"test-user-1751654515354"}
{"documentId":"test-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.362Z"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.366Z","totalConnections":1,"userId":"test-user-1751654515354"}
{"connectionId":"BCol_1xASC5fplh6AAAB","documentId":"test-document","duration":15,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.372Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"client namespace disconnect","remainingConnections":0,"service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.372Z","userId":"test-user-1751654515354"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:42:08.146Z"}
{"connectionId":"rfknqaFeZkOSlE8fAAAD","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:08.149Z","userId":"user-x637gema3"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:42:08.151Z","totalConnections":1,"userId":"user-x637gema3"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:33.546Z"}
{"connectionId":"bv2TsdH515RJW0NrAAAF","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:33.548Z","userId":"user-tws77kcuu"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:33.550Z","totalConnections":2,"userId":"user-tws77kcuu"}
{"connectionId":"bv2TsdH515RJW0NrAAAF","documentId":"test-document","duration":22635,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:56.183Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:56.185Z","userId":"user-tws77kcuu"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:42:58.058Z"}
{"connectionId":"iCYG7-QefPt2ffnLAAAH","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:58.061Z","userId":"user-nm83ygtbg"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:42:58.064Z","totalConnections":2,"userId":"user-nm83ygtbg"}
{"connectionId":"rfknqaFeZkOSlE8fAAAD","documentId":"test-document","duration":92407,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:40.555Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:43:40.560Z","userId":"user-x637gema3"}
{"connectionId":"iCYG7-QefPt2ffnLAAAH","documentId":"test-document","duration":42503,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:40.564Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:43:40.565Z","userId":"user-nm83ygtbg"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.267Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.271Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.272Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.279Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.279Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:43:48.039Z"}
{"connectionId":"EXbzHqsAGIjLdvPbAAAB","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:48.042Z","userId":"user-x637gema3"}
{"documentId":"test-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:48.044Z"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:43:48.046Z","totalConnections":1,"userId":"user-x637gema3"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:43:49.028Z"}
{"connectionId":"cmAERcca4yd_BtW0AAAD","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:49.029Z","userId":"user-nm83ygtbg"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:43:49.030Z","totalConnections":2,"userId":"user-nm83ygtbg"}
{"connectionId":"EXbzHqsAGIjLdvPbAAAB","documentId":"test-document","duration":50622,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:44:38.663Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:44:38.669Z","userId":"user-x637gema3"}
{"connectionId":"cmAERcca4yd_BtW0AAAD","documentId":"test-document","duration":55741,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:44:44.770Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:44:44.770Z","userId":"user-nm83ygtbg"}
