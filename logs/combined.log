{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.439Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.444Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.446Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
