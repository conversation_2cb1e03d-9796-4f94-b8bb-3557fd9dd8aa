const Y = require('yjs');

/**
 * WebSocket Handler for YJS Integration
 * Follows Single Responsibility Principle - handles only WebSocket events for YJS
 * Follows Dependency Inversion Principle - depends on abstractions
 */
class WebSocketHandler {
  constructor(connectionManager, documentManager, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.logger = logger;
  }

  /**
   * Handle new socket connection
   * @param {Object} socket - Socket.IO socket instance
   */
  handleConnection(socket) {
    this.logger.info('New socket connection', { socketId: socket.id });

    // Handle document join
    socket.on('join-document', (data) => {
      this.handleJoinDocument(socket, data);
    });

    // Handle YJS updates
    socket.on('yjs-update', (data) => {
      this.handleYjsUpdate(socket, data);
    });

    // Handle awareness updates
    socket.on('awareness-update', (data) => {
      this.handleAwarenessUpdate(socket, data);
    });

    // Handle sync request
    socket.on('sync-request', (data) => {
      this.handleSyncRequest(socket, data);
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      this.handleDisconnection(socket, reason);
    });

    // Handle errors
    socket.on('error', (error) => {
      this.handleError(socket, error);
    });
  }

  /**
   * Handle document join request
   */
  handleJoinDocument(socket, data) {
    try {
      const { documentId, userId, metadata = {} } = data;

      if (!documentId) {
        socket.emit('error', { message: 'Document ID is required' });
        return;
      }

      // Add connection to manager
      const connectionData = {
        documentId,
        userId,
        ...metadata
      };

      this.connectionManager.addConnection(socket.id, socket, connectionData);

      // Get or create document
      const doc = this.documentManager.getDocument(documentId);

      // Update connection count for document
      const connections = this.connectionManager.getConnectionsByDocument(documentId);
      this.documentManager.updateConnectionCount(documentId, connections.length);

      // Send current document state to new client
      const state = Y.encodeStateAsUpdate(doc);
      socket.emit('sync-response', {
        documentId,
        state: Array.from(state),
        type: 'full-sync'
      });

      // Notify other clients about new connection
      this.connectionManager.broadcast(documentId, {
        type: 'user-joined',
        userId,
        connectionId: socket.id
      }, socket.id);

      socket.emit('joined-document', {
        documentId,
        connectionId: socket.id,
        connectedUsers: connections.length
      });

      this.logger.info('Client joined document', {
        socketId: socket.id,
        documentId,
        userId,
        totalConnections: connections.length
      });

    } catch (error) {
      this.logger.error('Failed to handle document join', error, {
        socketId: socket.id,
        data
      });
      socket.emit('error', { message: 'Failed to join document' });
    }
  }

  /**
   * Handle YJS document updates
   */
  handleYjsUpdate(socket, data) {
    try {
      this.logger.info('Received YJS update', {
        socketId: socket.id,
        documentId: data?.documentId,
        updateLength: data?.update?.length
      });

      const { documentId, update } = data;
      const connection = this.connectionManager.getConnection(socket.id);

      if (!connection || connection.documentId !== documentId) {
        socket.emit('error', { message: 'Not authorized for this document' });
        return;
      }

      if (!update || !Array.isArray(update)) {
        socket.emit('error', { message: 'Invalid update format' });
        return;
      }

      // Convert array back to Uint8Array
      const updateArray = new Uint8Array(update);

      // Apply update to document
      this.documentManager.applyUpdate(documentId, updateArray, socket.id);

      // Broadcast update to other clients
      this.connectionManager.broadcast(documentId, {
        type: 'yjs-update',
        documentId,
        update,
        origin: socket.id
      }, socket.id);

      // Update connection activity
      this.connectionManager.updateLastActivity(socket.id);

      this.logger.debug('YJS update processed', {
        socketId: socket.id,
        documentId,
        updateSize: update.length
      });

    } catch (error) {
      this.logger.error('Failed to handle YJS update', error, {
        socketId: socket.id,
        data
      });
      socket.emit('error', { message: 'Failed to process update' });
    }
  }

  /**
   * Handle awareness updates (cursor positions, selections, etc.)
   */
  handleAwarenessUpdate(socket, data) {
    try {
      const { documentId, awareness } = data;
      const connection = this.connectionManager.getConnection(socket.id);

      if (!connection || connection.documentId !== documentId) {
        socket.emit('error', { message: 'Not authorized for this document' });
        return;
      }

      // Broadcast awareness update to other clients
      this.connectionManager.broadcast(documentId, {
        type: 'awareness-update',
        documentId,
        awareness,
        origin: socket.id
      }, socket.id);

      this.logger.debug('Awareness update processed', {
        socketId: socket.id,
        documentId
      });

    } catch (error) {
      this.logger.error('Failed to handle awareness update', error, {
        socketId: socket.id,
        data
      });
    }
  }

  /**
   * Handle sync request from client
   */
  handleSyncRequest(socket, data) {
    try {
      const { documentId, stateVector } = data;
      const connection = this.connectionManager.getConnection(socket.id);

      if (!connection || connection.documentId !== documentId) {
        socket.emit('error', { message: 'Not authorized for this document' });
        return;
      }

      const doc = this.documentManager.getDocument(documentId);
      
      let update;
      if (stateVector && Array.isArray(stateVector)) {
        // Client provided state vector, send diff
        const clientStateVector = new Uint8Array(stateVector);
        update = Y.encodeStateAsUpdate(doc, clientStateVector);
      } else {
        // Send full document state
        update = Y.encodeStateAsUpdate(doc);
      }

      socket.emit('sync-response', {
        documentId,
        state: Array.from(update),
        type: stateVector ? 'diff-sync' : 'full-sync'
      });

      this.logger.debug('Sync request processed', {
        socketId: socket.id,
        documentId,
        syncType: stateVector ? 'diff' : 'full'
      });

    } catch (error) {
      this.logger.error('Failed to handle sync request', error, {
        socketId: socket.id,
        data
      });
      socket.emit('error', { message: 'Failed to sync document' });
    }
  }

  /**
   * Handle client disconnection
   */
  handleDisconnection(socket, reason) {
    try {
      const connection = this.connectionManager.getConnection(socket.id);
      
      if (connection) {
        const { documentId, userId } = connection;

        // Remove connection
        this.connectionManager.removeConnection(socket.id);

        // Update document connection count
        if (documentId) {
          const remainingConnections = this.connectionManager.getConnectionsByDocument(documentId);
          this.documentManager.updateConnectionCount(documentId, remainingConnections.length);

          // Notify other clients about disconnection
          this.connectionManager.broadcast(documentId, {
            type: 'user-left',
            userId,
            connectionId: socket.id
          });
        }

        this.logger.info('Client disconnected', {
          socketId: socket.id,
          documentId,
          userId,
          reason,
          remainingConnections: documentId ? 
            this.connectionManager.getConnectionsByDocument(documentId).length : 0
        });
      }

    } catch (error) {
      this.logger.error('Failed to handle disconnection', error, {
        socketId: socket.id,
        reason
      });
    }
  }

  /**
   * Handle socket errors
   */
  handleError(socket, error) {
    this.logger.error('Socket error occurred', error, {
      socketId: socket.id
    });
  }
}

module.exports = WebSocketHandler;
